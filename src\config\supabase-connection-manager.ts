/**
 * Supabase Connection Manager
 * Enhanced connection management with monitoring, retry logic, and health checks
 */

import { Pool, PoolClient, PoolConfig } from 'pg';
import logger from '../utils/logger';

interface ConnectionMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingClients: number;
  lastConnectionTime: Date | null;
  connectionErrors: number;
  queryCount: number;
  avgQueryTime: number;
}

interface HealthCheckResult {
  isHealthy: boolean;
  latency: number;
  error?: string;
  timestamp: Date;
}

class SupabaseConnectionManager {
  private pool: Pool;
  private metrics: ConnectionMetrics;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastHealthCheck: HealthCheckResult | null = null;
  private connectionRetryCount = 0;
  private maxRetries = 3;

  constructor() {
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingClients: 0,
      lastConnectionTime: null,
      connectionErrors: 0,
      queryCount: 0,
      avgQueryTime: 0
    };

    this.initializePool();
    this.setupHealthChecks();
    this.setupMetricsCollection();
  }

  private initializePool(): void {
    const poolConfig: PoolConfig = {
      host: process.env.PGHOST,
      user: process.env.PGUSER,
      password: process.env.PGPASSWORD,
      database: process.env.PGDATABASE,
      port: parseInt(process.env.PGPORT || '5432'),
      // Supabase requires SSL
      ssl: process.env.PGSSLMODE === 'require' ? {
        rejectUnauthorized: false
      } : false,
      // Optimized for Supabase cloud database
      max: parseInt(process.env.PG_MAX_CONNECTIONS || '15'),
      min: parseInt(process.env.PG_MIN_CONNECTIONS || '3'),
      idleTimeoutMillis: parseInt(process.env.PG_IDLE_TIMEOUT || '30000'),
      connectionTimeoutMillis: 10000, // Increased for cloud latency
      acquireTimeoutMillis: 5000,
      keepAlive: true,
      statement_timeout: 30000,
      query_timeout: 30000,
      application_name: `tempmail-api-${process.env.INSTANCE_ID || 'unknown'}`,
    };

    this.pool = new Pool(poolConfig);
    this.setupPoolEventHandlers();

    logger.info('Supabase connection pool initialized', {
      host: poolConfig.host,
      database: poolConfig.database,
      maxConnections: poolConfig.max,
      minConnections: poolConfig.min,
      ssl: !!poolConfig.ssl
    });
  }

  private setupPoolEventHandlers(): void {
    this.pool.on('connect', (client: PoolClient) => {
      this.metrics.totalConnections++;
      this.metrics.lastConnectionTime = new Date();
      this.connectionRetryCount = 0; // Reset retry count on successful connection
      
      logger.debug('New client connected to Supabase', {
        totalConnections: this.pool.totalCount,
        idleConnections: this.pool.idleCount,
        waitingClients: this.pool.waitingCount
      });
    });

    this.pool.on('remove', (client: PoolClient) => {
      logger.debug('Client removed from Supabase pool', {
        totalConnections: this.pool.totalCount,
        idleConnections: this.pool.idleCount
      });
    });

    this.pool.on('error', (err: Error) => {
      this.metrics.connectionErrors++;
      logger.error('Supabase pool error', {
        error: err.message,
        code: (err as any).code,
        connectionErrors: this.metrics.connectionErrors
      });

      // Implement exponential backoff for reconnection
      if (this.connectionRetryCount < this.maxRetries) {
        this.connectionRetryCount++;
        const retryDelay = Math.pow(2, this.connectionRetryCount) * 1000;
        
        logger.warn(`Attempting to reconnect to Supabase in ${retryDelay}ms (attempt ${this.connectionRetryCount}/${this.maxRetries})`);
        
        setTimeout(() => {
          this.initializePool();
        }, retryDelay);
      } else {
        logger.error('Max connection retries exceeded. Manual intervention required.');
      }
    });
  }

  private setupHealthChecks(): void {
    // Perform health check every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000);

    // Initial health check
    setTimeout(() => this.performHealthCheck(), 5000);
  }

  private async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const client = await this.pool.connect();
      const result = await client.query('SELECT 1 as health_check');
      client.release();
      
      const latency = Date.now() - startTime;
      
      this.lastHealthCheck = {
        isHealthy: true,
        latency,
        timestamp: new Date()
      };

      if (latency > 1000) {
        logger.warn('High Supabase latency detected', { latency });
      }

      return this.lastHealthCheck;
    } catch (error) {
      const latency = Date.now() - startTime;
      
      this.lastHealthCheck = {
        isHealthy: false,
        latency,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date()
      };

      logger.error('Supabase health check failed', {
        error: this.lastHealthCheck.error,
        latency
      });

      return this.lastHealthCheck;
    }
  }

  private setupMetricsCollection(): void {
    // Update metrics every 10 seconds
    setInterval(() => {
      this.updateMetrics();
    }, 10000);
  }

  private updateMetrics(): void {
    this.metrics.activeConnections = this.pool.totalCount - this.pool.idleCount;
    this.metrics.idleConnections = this.pool.idleCount;
    this.metrics.waitingClients = this.pool.waitingCount;

    // Log metrics if there are performance concerns
    if (this.metrics.waitingClients > 0 || this.metrics.activeConnections > 12) {
      logger.warn('Supabase connection pool under pressure', {
        activeConnections: this.metrics.activeConnections,
        idleConnections: this.metrics.idleConnections,
        waitingClients: this.metrics.waitingClients,
        maxConnections: parseInt(process.env.PG_MAX_CONNECTIONS || '15')
      });
    }
  }

  public async getClient(): Promise<PoolClient> {
    const startTime = Date.now();
    
    try {
      const client = await this.pool.connect();
      const acquisitionTime = Date.now() - startTime;
      
      if (acquisitionTime > 1000) {
        logger.warn('Slow connection acquisition from Supabase pool', {
          acquisitionTime,
          activeConnections: this.metrics.activeConnections,
          waitingClients: this.metrics.waitingClients
        });
      }
      
      return client;
    } catch (error) {
      this.metrics.connectionErrors++;
      logger.error('Failed to acquire Supabase connection', {
        error: error instanceof Error ? error.message : String(error),
        acquisitionTime: Date.now() - startTime,
        connectionErrors: this.metrics.connectionErrors
      });
      throw error;
    }
  }

  public async query(text: string, params?: any[]): Promise<any> {
    const startTime = Date.now();
    let client: PoolClient | null = null;
    
    try {
      client = await this.getClient();
      const result = await client.query(text, params);
      
      const queryTime = Date.now() - startTime;
      this.metrics.queryCount++;
      
      // Update average query time
      this.metrics.avgQueryTime = (this.metrics.avgQueryTime * (this.metrics.queryCount - 1) + queryTime) / this.metrics.queryCount;
      
      if (queryTime > 5000) {
        logger.warn('Slow Supabase query detected', {
          queryTime,
          query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
          avgQueryTime: this.metrics.avgQueryTime
        });
      }
      
      return result;
    } catch (error) {
      logger.error('Supabase query failed', {
        error: error instanceof Error ? error.message : String(error),
        query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        queryTime: Date.now() - startTime
      });
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  public getMetrics(): ConnectionMetrics {
    return { ...this.metrics };
  }

  public getHealthStatus(): HealthCheckResult | null {
    return this.lastHealthCheck;
  }

  public async close(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    await this.pool.end();
    logger.info('Supabase connection pool closed');
  }
}

// Export singleton instance
export const supabaseConnectionManager = new SupabaseConnectionManager();
export default supabaseConnectionManager;
