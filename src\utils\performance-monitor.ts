/**
 * Performance Monitor for Supabase Migration
 * Tracks database performance, query times, and connection health
 */

import logger from './logger';

interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

interface PerformanceMetrics {
  totalQueries: number;
  avgQueryTime: number;
  slowQueries: number;
  errorRate: number;
  connectionIssues: number;
  lastSlowQuery?: QueryMetrics;
  recentQueries: QueryMetrics[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private queryHistory: QueryMetrics[] = [];
  private readonly maxHistorySize = 1000;
  private readonly slowQueryThreshold = 1000; // 1 second
  private readonly maxRecentQueries = 10;

  constructor() {
    this.metrics = {
      totalQueries: 0,
      avgQueryTime: 0,
      slowQueries: 0,
      errorRate: 0,
      connectionIssues: 0,
      recentQueries: []
    };

    // Log performance summary every 5 minutes
    setInterval(() => {
      this.logPerformanceSummary();
    }, 5 * 60 * 1000);
  }

  /**
   * Record a database query execution
   */
  public recordQuery(query: string, duration: number, success: boolean, error?: string): void {
    const queryMetric: QueryMetrics = {
      query: this.sanitizeQuery(query),
      duration,
      timestamp: new Date(),
      success,
      error
    };

    // Add to history
    this.queryHistory.push(queryMetric);
    if (this.queryHistory.length > this.maxHistorySize) {
      this.queryHistory.shift();
    }

    // Update metrics
    this.updateMetrics(queryMetric);

    // Log slow queries
    if (duration > this.slowQueryThreshold) {
      this.logSlowQuery(queryMetric);
    }

    // Log errors
    if (!success && error) {
      this.logQueryError(queryMetric);
    }
  }

  /**
   * Record a connection issue
   */
  public recordConnectionIssue(error: string): void {
    this.metrics.connectionIssues++;
    
    logger.warn('Database connection issue recorded', {
      error,
      totalConnectionIssues: this.metrics.connectionIssues,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      recentQueries: this.getRecentQueries()
    };
  }

  /**
   * Get performance summary for the last period
   */
  public getPerformanceSummary(minutes: number = 60): any {
    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    const recentQueries = this.queryHistory.filter(q => q.timestamp > cutoffTime);

    if (recentQueries.length === 0) {
      return {
        period: `${minutes} minutes`,
        totalQueries: 0,
        avgQueryTime: 0,
        slowQueries: 0,
        errorRate: 0,
        message: 'No queries in the specified period'
      };
    }

    const totalDuration = recentQueries.reduce((sum, q) => sum + q.duration, 0);
    const slowQueries = recentQueries.filter(q => q.duration > this.slowQueryThreshold);
    const errorQueries = recentQueries.filter(q => !q.success);

    return {
      period: `${minutes} minutes`,
      totalQueries: recentQueries.length,
      avgQueryTime: Math.round(totalDuration / recentQueries.length),
      slowQueries: slowQueries.length,
      errorRate: Math.round((errorQueries.length / recentQueries.length) * 100),
      slowestQuery: slowQueries.length > 0 ? 
        slowQueries.reduce((slowest, current) => 
          current.duration > slowest.duration ? current : slowest
        ) : null,
      queryTypes: this.analyzeQueryTypes(recentQueries)
    };
  }

  /**
   * Check if performance is degraded
   */
  public isPerformanceDegraded(): boolean {
    const recentSummary = this.getPerformanceSummary(15); // Last 15 minutes
    
    return (
      recentSummary.avgQueryTime > 2000 || // Average query time > 2 seconds
      recentSummary.errorRate > 5 || // Error rate > 5%
      recentSummary.slowQueries > recentSummary.totalQueries * 0.3 // > 30% slow queries
    );
  }

  /**
   * Get recommendations based on current performance
   */
  public getPerformanceRecommendations(): string[] {
    const recommendations: string[] = [];
    const summary = this.getPerformanceSummary(60);

    if (summary.avgQueryTime > 1500) {
      recommendations.push('Consider optimizing slow queries or adding indexes');
    }

    if (summary.errorRate > 3) {
      recommendations.push('High error rate detected - check connection stability');
    }

    if (this.metrics.connectionIssues > 10) {
      recommendations.push('Frequent connection issues - consider increasing connection pool size');
    }

    if (summary.slowQueries > summary.totalQueries * 0.2) {
      recommendations.push('High percentage of slow queries - review query performance');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is within acceptable parameters');
    }

    return recommendations;
  }

  private updateMetrics(queryMetric: QueryMetrics): void {
    this.metrics.totalQueries++;
    
    // Update average query time
    this.metrics.avgQueryTime = (
      (this.metrics.avgQueryTime * (this.metrics.totalQueries - 1)) + queryMetric.duration
    ) / this.metrics.totalQueries;

    // Update slow query count
    if (queryMetric.duration > this.slowQueryThreshold) {
      this.metrics.slowQueries++;
      this.metrics.lastSlowQuery = queryMetric;
    }

    // Update error rate
    if (!queryMetric.success) {
      const errorQueries = this.queryHistory.filter(q => !q.success).length;
      this.metrics.errorRate = (errorQueries / this.metrics.totalQueries) * 100;
    }
  }

  private getRecentQueries(): QueryMetrics[] {
    return this.queryHistory
      .slice(-this.maxRecentQueries)
      .reverse(); // Most recent first
  }

  private sanitizeQuery(query: string): string {
    // Remove sensitive data and truncate long queries
    return query
      .replace(/\$\d+/g, '?') // Replace parameter placeholders
      .substring(0, 200) + (query.length > 200 ? '...' : '');
  }

  private logSlowQuery(queryMetric: QueryMetrics): void {
    logger.warn('Slow query detected', {
      query: queryMetric.query,
      duration: queryMetric.duration,
      threshold: this.slowQueryThreshold,
      totalSlowQueries: this.metrics.slowQueries,
      timestamp: queryMetric.timestamp.toISOString()
    });
  }

  private logQueryError(queryMetric: QueryMetrics): void {
    logger.error('Query execution failed', {
      query: queryMetric.query,
      duration: queryMetric.duration,
      error: queryMetric.error,
      errorRate: this.metrics.errorRate,
      timestamp: queryMetric.timestamp.toISOString()
    });
  }

  private logPerformanceSummary(): void {
    const summary = this.getPerformanceSummary(5); // Last 5 minutes
    const recommendations = this.getPerformanceRecommendations();

    logger.info('Performance summary', {
      ...summary,
      recommendations,
      isDegraded: this.isPerformanceDegraded()
    });
  }

  private analyzeQueryTypes(queries: QueryMetrics[]): Record<string, number> {
    const types: Record<string, number> = {};
    
    queries.forEach(query => {
      const type = this.getQueryType(query.query);
      types[type] = (types[type] || 0) + 1;
    });

    return types;
  }

  private getQueryType(query: string): string {
    const normalizedQuery = query.toLowerCase().trim();
    
    if (normalizedQuery.startsWith('select')) return 'SELECT';
    if (normalizedQuery.startsWith('insert')) return 'INSERT';
    if (normalizedQuery.startsWith('update')) return 'UPDATE';
    if (normalizedQuery.startsWith('delete')) return 'DELETE';
    if (normalizedQuery.startsWith('with')) return 'CTE';
    
    return 'OTHER';
  }

  /**
   * Reset metrics (useful for testing or periodic resets)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalQueries: 0,
      avgQueryTime: 0,
      slowQueries: 0,
      errorRate: 0,
      connectionIssues: 0,
      recentQueries: []
    };
    this.queryHistory = [];
    
    logger.info('Performance metrics reset');
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();
export default performanceMonitor;
