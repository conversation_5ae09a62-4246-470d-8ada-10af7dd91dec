/**
 * Migration Validator for Supabase
 * Validates that the migration was successful and all components are working
 */

import { enhancedDatabase } from '../config/enhanced-database';
import logger from './logger';

interface ValidationResult {
  component: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

interface MigrationValidationReport {
  overall: 'pass' | 'fail' | 'warning';
  timestamp: string;
  results: ValidationResult[];
  summary: {
    passed: number;
    failed: number;
    warnings: number;
    total: number;
  };
}

class MigrationValidator {
  private readonly expectedTables = [
    'domains',
    'inboxes',
    'emails',
    'attachments',
    'api_keys',
    'forwarding_verifications',
    'usage_logs',
    'rapidapi_usage',
    'maintenance_logs'
  ];

  private readonly expectedExtensions = [
    'uuid-ossp',
    'pgcrypto',
    'pg_cron'
  ];

  private readonly expectedFunctions = [
    'cleanup_expired_inboxes',
    'purge_old_maintenance_logs',
    'sync_username_with_name'
  ];

  private readonly expectedIndexes = [
    'idx_inboxes_address',
    'idx_inboxes_rapidapi_key',
    'idx_emails_inbox_id',
    'idx_inboxes_composite'
  ];

  /**
   * Run complete migration validation
   */
  public async validateMigration(): Promise<MigrationValidationReport> {
    const results: ValidationResult[] = [];
    
    logger.info('Starting Supabase migration validation...');

    try {
      // Test basic connectivity
      results.push(await this.validateConnectivity());

      // Validate extensions
      results.push(...await this.validateExtensions());

      // Validate tables
      results.push(...await this.validateTables());

      // Validate indexes
      results.push(...await this.validateIndexes());

      // Validate functions
      results.push(...await this.validateFunctions());

      // Validate pg_cron jobs
      results.push(...await this.validateCronJobs());

      // Validate default data
      results.push(await this.validateDefaultData());

      // Test basic operations
      results.push(...await this.validateBasicOperations());

      // Performance validation
      results.push(await this.validatePerformance());

    } catch (error) {
      results.push({
        component: 'validation_process',
        status: 'fail',
        message: 'Validation process failed',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    const summary = this.calculateSummary(results);
    const overall = this.determineOverallStatus(summary);

    const report: MigrationValidationReport = {
      overall,
      timestamp: new Date().toISOString(),
      results,
      summary
    };

    this.logValidationReport(report);
    return report;
  }

  private async validateConnectivity(): Promise<ValidationResult> {
    try {
      const result = await enhancedDatabase.query('SELECT NOW() as current_time, version() as version');
      
      return {
        component: 'connectivity',
        status: 'pass',
        message: 'Successfully connected to Supabase',
        details: {
          currentTime: result.rows[0]?.current_time,
          postgresVersion: result.rows[0]?.version?.split(' ')[0]
        }
      };
    } catch (error) {
      return {
        component: 'connectivity',
        status: 'fail',
        message: 'Failed to connect to Supabase',
        details: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }

  private async validateExtensions(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    try {
      const result = await enhancedDatabase.query(`
        SELECT extname FROM pg_extension 
        WHERE extname = ANY($1)
      `, [this.expectedExtensions]);

      const installedExtensions = result.rows.map((row: any) => row.extname);

      for (const extension of this.expectedExtensions) {
        if (installedExtensions.includes(extension)) {
          results.push({
            component: `extension_${extension}`,
            status: 'pass',
            message: `Extension ${extension} is installed`
          });
        } else {
          results.push({
            component: `extension_${extension}`,
            status: 'fail',
            message: `Extension ${extension} is missing`
          });
        }
      }
    } catch (error) {
      results.push({
        component: 'extensions',
        status: 'fail',
        message: 'Failed to check extensions',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    return results;
  }

  private async validateTables(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    try {
      const result = await enhancedDatabase.query(`
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = ANY($1)
      `, [this.expectedTables]);

      const existingTables = result.rows.map((row: any) => row.table_name);

      for (const table of this.expectedTables) {
        if (existingTables.includes(table)) {
          // Check table structure
          const columnResult = await enhancedDatabase.query(`
            SELECT COUNT(*) as column_count 
            FROM information_schema.columns 
            WHERE table_name = $1 AND table_schema = 'public'
          `, [table]);

          results.push({
            component: `table_${table}`,
            status: 'pass',
            message: `Table ${table} exists`,
            details: { columnCount: columnResult.rows[0]?.column_count }
          });
        } else {
          results.push({
            component: `table_${table}`,
            status: 'fail',
            message: `Table ${table} is missing`
          });
        }
      }
    } catch (error) {
      results.push({
        component: 'tables',
        status: 'fail',
        message: 'Failed to check tables',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    return results;
  }

  private async validateIndexes(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    try {
      const result = await enhancedDatabase.query(`
        SELECT indexname FROM pg_indexes 
        WHERE schemaname = 'public' AND indexname = ANY($1)
      `, [this.expectedIndexes]);

      const existingIndexes = result.rows.map((row: any) => row.indexname);

      for (const index of this.expectedIndexes) {
        if (existingIndexes.includes(index)) {
          results.push({
            component: `index_${index}`,
            status: 'pass',
            message: `Index ${index} exists`
          });
        } else {
          results.push({
            component: `index_${index}`,
            status: 'warning',
            message: `Index ${index} is missing - may impact performance`
          });
        }
      }
    } catch (error) {
      results.push({
        component: 'indexes',
        status: 'fail',
        message: 'Failed to check indexes',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    return results;
  }

  private async validateFunctions(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    try {
      const result = await enhancedDatabase.query(`
        SELECT routine_name FROM information_schema.routines 
        WHERE routine_schema = 'public' AND routine_name = ANY($1)
      `, [this.expectedFunctions]);

      const existingFunctions = result.rows.map((row: any) => row.routine_name);

      for (const func of this.expectedFunctions) {
        if (existingFunctions.includes(func)) {
          results.push({
            component: `function_${func}`,
            status: 'pass',
            message: `Function ${func} exists`
          });
        } else {
          results.push({
            component: `function_${func}`,
            status: 'fail',
            message: `Function ${func} is missing`
          });
        }
      }
    } catch (error) {
      results.push({
        component: 'functions',
        status: 'fail',
        message: 'Failed to check functions',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    return results;
  }

  private async validateCronJobs(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    try {
      const result = await enhancedDatabase.query(`
        SELECT jobname, schedule, active FROM cron.job 
        WHERE jobname IN ('cleanup-expired-inboxes', 'purge-old-maintenance-logs')
      `);

      const expectedJobs = ['cleanup-expired-inboxes', 'purge-old-maintenance-logs'];
      const existingJobs = result.rows.map((row: any) => row.jobname);

      for (const job of expectedJobs) {
        const jobInfo = result.rows.find((row: any) => row.jobname === job);
        
        if (jobInfo) {
          results.push({
            component: `cron_job_${job}`,
            status: jobInfo.active ? 'pass' : 'warning',
            message: `Cron job ${job} ${jobInfo.active ? 'is active' : 'exists but is inactive'}`,
            details: { schedule: jobInfo.schedule, active: jobInfo.active }
          });
        } else {
          results.push({
            component: `cron_job_${job}`,
            status: 'fail',
            message: `Cron job ${job} is missing`
          });
        }
      }
    } catch (error) {
      results.push({
        component: 'cron_jobs',
        status: 'warning',
        message: 'Failed to check pg_cron jobs - may not have permissions',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    return results;
  }

  private async validateDefaultData(): Promise<ValidationResult> {
    try {
      const result = await enhancedDatabase.query('SELECT COUNT(*) as domain_count FROM domains');
      const domainCount = parseInt(result.rows[0]?.domain_count || '0');

      if (domainCount >= 12) {
        return {
          component: 'default_data',
          status: 'pass',
          message: `Default domains loaded (${domainCount} domains)`,
          details: { domainCount }
        };
      } else if (domainCount > 0) {
        return {
          component: 'default_data',
          status: 'warning',
          message: `Partial default data (${domainCount} domains, expected 12+)`,
          details: { domainCount }
        };
      } else {
        return {
          component: 'default_data',
          status: 'fail',
          message: 'No default domains found',
          details: { domainCount }
        };
      }
    } catch (error) {
      return {
        component: 'default_data',
        status: 'fail',
        message: 'Failed to check default data',
        details: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }

  private async validateBasicOperations(): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Test UUID generation
    try {
      const result = await enhancedDatabase.query('SELECT uuid_generate_v4() as test_uuid');
      results.push({
        component: 'uuid_generation',
        status: 'pass',
        message: 'UUID generation working',
        details: { testUuid: result.rows[0]?.test_uuid }
      });
    } catch (error) {
      results.push({
        component: 'uuid_generation',
        status: 'fail',
        message: 'UUID generation failed',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    // Test JSONB operations
    try {
      await enhancedDatabase.query(`SELECT '{"test": true}'::jsonb as test_json`);
      results.push({
        component: 'jsonb_support',
        status: 'pass',
        message: 'JSONB operations working'
      });
    } catch (error) {
      results.push({
        component: 'jsonb_support',
        status: 'fail',
        message: 'JSONB operations failed',
        details: { error: error instanceof Error ? error.message : String(error) }
      });
    }

    return results;
  }

  private async validatePerformance(): Promise<ValidationResult> {
    const startTime = Date.now();
    
    try {
      // Run a simple performance test
      await enhancedDatabase.query('SELECT COUNT(*) FROM domains');
      const queryTime = Date.now() - startTime;

      if (queryTime < 500) {
        return {
          component: 'performance',
          status: 'pass',
          message: 'Database performance is good',
          details: { queryTime }
        };
      } else if (queryTime < 2000) {
        return {
          component: 'performance',
          status: 'warning',
          message: 'Database performance is acceptable but could be better',
          details: { queryTime }
        };
      } else {
        return {
          component: 'performance',
          status: 'warning',
          message: 'Database performance is slow',
          details: { queryTime }
        };
      }
    } catch (error) {
      return {
        component: 'performance',
        status: 'fail',
        message: 'Performance test failed',
        details: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }

  private calculateSummary(results: ValidationResult[]) {
    const passed = results.filter(r => r.status === 'pass').length;
    const failed = results.filter(r => r.status === 'fail').length;
    const warnings = results.filter(r => r.status === 'warning').length;

    return {
      passed,
      failed,
      warnings,
      total: results.length
    };
  }

  private determineOverallStatus(summary: any): 'pass' | 'fail' | 'warning' {
    if (summary.failed > 0) return 'fail';
    if (summary.warnings > 0) return 'warning';
    return 'pass';
  }

  private logValidationReport(report: MigrationValidationReport): void {
    logger.info('Migration validation completed', {
      overall: report.overall,
      summary: report.summary,
      timestamp: report.timestamp
    });

    // Log failed components
    const failedComponents = report.results.filter(r => r.status === 'fail');
    if (failedComponents.length > 0) {
      logger.error('Failed validation components', {
        components: failedComponents.map(c => ({ component: c.component, message: c.message }))
      });
    }

    // Log warnings
    const warningComponents = report.results.filter(r => r.status === 'warning');
    if (warningComponents.length > 0) {
      logger.warn('Validation warnings', {
        components: warningComponents.map(c => ({ component: c.component, message: c.message }))
      });
    }
  }
}

export const migrationValidator = new MigrationValidator();
export default migrationValidator;
