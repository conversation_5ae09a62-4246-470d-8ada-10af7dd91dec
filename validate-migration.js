#!/usr/bin/env node

/**
 * Migration Validation Script
 * This script validates that the Supabase migration was successful
 * Run with: node validate-migration.js
 */

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  database: process.env.PGDATABASE,
  port: parseInt(process.env.PGPORT || '5432'),
  ssl: process.env.PGSSLMODE === 'require' ? {
    rejectUnauthorized: false
  } : false,
  max: 5,
  min: 1,
  idleTimeoutMillis: 30000,
});

const expectedTables = [
  'domains',
  'inboxes', 
  'emails',
  'attachments',
  'api_keys',
  'forwarding_verifications',
  'usage_logs',
  'rapidapi_usage',
  'maintenance_logs'
];

const expectedExtensions = ['uuid-ossp', 'pgcrypto', 'pg_cron'];

async function validateMigration() {
  let client;
  let allChecksPass = true;

  try {
    console.log('🔍 Starting Migration Validation...\n');
    
    client = await pool.connect();

    // 1. Validate Extensions
    console.log('📦 Checking Extensions...');
    const extensionsResult = await client.query(`
      SELECT extname FROM pg_extension 
      WHERE extname IN ('uuid-ossp', 'pgcrypto', 'pg_cron')
    `);
    
    const installedExtensions = extensionsResult.rows.map(row => row.extname);
    
    for (const ext of expectedExtensions) {
      if (installedExtensions.includes(ext)) {
        console.log(`   ✅ ${ext}`);
      } else {
        console.log(`   ❌ ${ext} - MISSING`);
        allChecksPass = false;
      }
    }

    // 2. Validate Tables
    console.log('\n📋 Checking Tables...');
    const tablesResult = await client.query(`
      SELECT table_name FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    const existingTables = tablesResult.rows.map(row => row.table_name);
    
    for (const table of expectedTables) {
      if (existingTables.includes(table)) {
        console.log(`   ✅ ${table}`);
      } else {
        console.log(`   ❌ ${table} - MISSING`);
        allChecksPass = false;
      }
    }

    // 3. Validate Indexes
    console.log('\n🔍 Checking Critical Indexes...');
    const indexesResult = await client.query(`
      SELECT indexname FROM pg_indexes 
      WHERE schemaname = 'public' 
      AND indexname IN (
        'idx_inboxes_address',
        'idx_inboxes_rapidapi_key', 
        'idx_emails_inbox_id',
        'idx_inboxes_composite'
      )
    `);
    
    const existingIndexes = indexesResult.rows.map(row => row.indexname);
    const criticalIndexes = [
      'idx_inboxes_address',
      'idx_inboxes_rapidapi_key',
      'idx_emails_inbox_id', 
      'idx_inboxes_composite'
    ];
    
    for (const index of criticalIndexes) {
      if (existingIndexes.includes(index)) {
        console.log(`   ✅ ${index}`);
      } else {
        console.log(`   ❌ ${index} - MISSING`);
        allChecksPass = false;
      }
    }

    // 4. Validate Functions
    console.log('\n⚙️  Checking Functions...');
    const functionsResult = await client.query(`
      SELECT routine_name FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      AND routine_name IN ('cleanup_expired_inboxes', 'purge_old_maintenance_logs')
    `);
    
    const existingFunctions = functionsResult.rows.map(row => row.routine_name);
    const expectedFunctions = ['cleanup_expired_inboxes', 'purge_old_maintenance_logs'];
    
    for (const func of expectedFunctions) {
      if (existingFunctions.includes(func)) {
        console.log(`   ✅ ${func}`);
      } else {
        console.log(`   ❌ ${func} - MISSING`);
        allChecksPass = false;
      }
    }

    // 5. Validate pg_cron Jobs
    if (installedExtensions.includes('pg_cron')) {
      console.log('\n⏰ Checking pg_cron Jobs...');
      try {
        const cronResult = await client.query(`
          SELECT jobname, schedule FROM cron.job 
          WHERE jobname IN ('cleanup-expired-inboxes', 'purge-old-maintenance-logs')
        `);
        
        const existingJobs = cronResult.rows.map(row => row.jobname);
        const expectedJobs = ['cleanup-expired-inboxes', 'purge-old-maintenance-logs'];
        
        for (const job of expectedJobs) {
          if (existingJobs.includes(job)) {
            const jobInfo = cronResult.rows.find(row => row.jobname === job);
            console.log(`   ✅ ${job} (${jobInfo.schedule})`);
          } else {
            console.log(`   ❌ ${job} - MISSING`);
            allChecksPass = false;
          }
        }
      } catch (error) {
        console.log('   ⚠️  Could not check cron jobs - may need permissions');
      }
    }

    // 6. Validate Default Domains
    console.log('\n🌐 Checking Default Domains...');
    const domainsResult = await client.query('SELECT COUNT(*) as count FROM domains');
    const domainCount = parseInt(domainsResult.rows[0].count);
    
    if (domainCount >= 12) {
      console.log(`   ✅ ${domainCount} domains found`);
    } else {
      console.log(`   ⚠️  Only ${domainCount} domains found (expected 12+)`);
    }

    // 7. Test Basic Operations
    console.log('\n🧪 Testing Basic Operations...');
    
    // Test UUID generation
    const uuidResult = await client.query('SELECT uuid_generate_v4() as test_uuid');
    console.log(`   ✅ UUID generation: ${uuidResult.rows[0].test_uuid}`);
    
    // Test current timestamp
    const timeResult = await client.query('SELECT NOW() as current_time');
    console.log(`   ✅ Timestamp: ${timeResult.rows[0].current_time}`);

    // Final Summary
    console.log('\n' + '='.repeat(50));
    if (allChecksPass) {
      console.log('🎉 MIGRATION VALIDATION SUCCESSFUL!');
      console.log('✅ All checks passed - your Supabase database is ready');
    } else {
      console.log('❌ MIGRATION VALIDATION FAILED!');
      console.log('⚠️  Some components are missing - please review the errors above');
    }
    console.log('='.repeat(50));

  } catch (error) {
    console.error('\n❌ Validation failed with error:');
    console.error(error.message);
    allChecksPass = false;
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }

  process.exit(allChecksPass ? 0 : 1);
}

// Run validation
validateMigration().catch(console.error);
