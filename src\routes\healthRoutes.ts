/**
 * Health Check Routes
 * Enhanced health monitoring for CockroachDB migration
 */

import { Router, Request, Response } from 'express';
import { cockroachDBConnectionManager } from '../config/supabase-connection-manager';
import { isRedisConnected } from '../config/redis-direct';
import logger from '../utils/logger';

const router = Router();

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      latency?: number;
      error?: string;
      metrics: {
        activeConnections: number;
        idleConnections: number;
        waitingClients: number;
        totalQueries: number;
        avgQueryTime: number;
        connectionErrors: number;
      };
    };
    redis: {
      status: 'healthy' | 'unhealthy';
      connected: boolean;
    };
  };
  environment: {
    nodeEnv: string;
    instanceId: string;
    pgHost: string;
    sslMode: string;
  };
}

/**
 * Basic health check endpoint
 * GET /healthz
 */
router.get('/healthz', async (req: Request, res: Response) => {
  try {
    const dbHealth = cockroachDBConnectionManager.getHealthStatus();
    const dbMetrics = cockroachDBConnectionManager.getMetrics();
    
    const isDbHealthy = dbHealth?.isHealthy ?? false;
    const isRedisHealthy = isRedisConnected();
    
    const overallStatus = isDbHealthy && isRedisHealthy ? 'healthy' : 
                         isDbHealthy || isRedisHealthy ? 'degraded' : 'unhealthy';

    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      services: {
        database: {
          status: isDbHealthy ? 'healthy' : 'unhealthy',
          latency: dbHealth?.latency,
          error: dbHealth?.error,
          metrics: {
            activeConnections: dbMetrics.activeConnections,
            idleConnections: dbMetrics.idleConnections,
            waitingClients: dbMetrics.waitingClients,
            totalQueries: dbMetrics.queryCount,
            avgQueryTime: Math.round(dbMetrics.avgQueryTime),
            connectionErrors: dbMetrics.connectionErrors
          }
        },
        redis: {
          status: isRedisHealthy ? 'healthy' : 'unhealthy',
          connected: isRedisHealthy
        }
      },
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        instanceId: process.env.INSTANCE_ID || 'unknown',
        pgHost: process.env.PGHOST || 'unknown',
        sslMode: process.env.PGSSLMODE || 'unknown'
      }
    };

    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    res.status(statusCode).json(healthStatus);

    // Log unhealthy status
    if (overallStatus !== 'healthy') {
      logger.warn('Health check returned non-healthy status', {
        status: overallStatus,
        dbHealthy: isDbHealthy,
        redisHealthy: isRedisHealthy,
        dbError: dbHealth?.error
      });
    }

  } catch (error) {
    logger.error('Health check endpoint error', {
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

/**
 * Detailed database health check
 * GET /health/database
 */
router.get('/health/database', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    
    // Perform a test query
    const result = await cockroachDBConnectionManager.query(
      'SELECT NOW() as current_time, version() as cockroachdb_version'
    );

    const queryTime = Date.now() - startTime;
    const dbHealth = cockroachDBConnectionManager.getHealthStatus();
    const dbMetrics = cockroachDBConnectionManager.getMetrics();

    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      latency: queryTime,
      lastHealthCheck: dbHealth,
      metrics: dbMetrics,
      testQuery: {
        currentTime: result.rows[0]?.current_time,
        cockroachdbVersion: result.rows[0]?.cockroachdb_version?.split(' ')[0]
      },
      connectionPool: {
        max: parseInt(process.env.PG_MAX_CONNECTIONS || '15'),
        min: parseInt(process.env.PG_MIN_CONNECTIONS || '3'),
        current: {
          total: dbMetrics.activeConnections + dbMetrics.idleConnections,
          active: dbMetrics.activeConnections,
          idle: dbMetrics.idleConnections,
          waiting: dbMetrics.waitingClients
        }
      }
    };

    res.json(response);

  } catch (error) {
    logger.error('Database health check failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Database health check failed'
    });
  }
});

/**
 * Redis health check
 * GET /health/redis
 */
router.get('/health/redis', async (req: Request, res: Response) => {
  try {
    const isConnected = isRedisConnected();
    
    const response = {
      status: isConnected ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      connected: isConnected,
      configuration: {
        enabled: process.env.REDIS_ENABLED === 'true',
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT
      }
    };

    res.status(isConnected ? 200 : 503).json(response);

  } catch (error) {
    logger.error('Redis health check failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Redis health check failed'
    });
  }
});

/**
 * Performance metrics endpoint
 * GET /health/metrics
 */
router.get('/health/metrics', async (req: Request, res: Response) => {
  try {
    const dbMetrics = cockroachDBConnectionManager.getMetrics();
    const dbHealth = cockroachDBConnectionManager.getHealthStatus();
    
    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: {
        health: dbHealth,
        metrics: dbMetrics,
        connectionPool: {
          utilization: dbMetrics.activeConnections / parseInt(process.env.PG_MAX_CONNECTIONS || '15'),
          efficiency: dbMetrics.idleConnections > 0 ? 'good' : 'stressed'
        }
      },
      redis: {
        connected: isRedisConnected(),
        enabled: process.env.REDIS_ENABLED === 'true'
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        nodeEnv: process.env.NODE_ENV,
        instanceId: process.env.INSTANCE_ID
      }
    };

    res.json(metrics);

  } catch (error) {
    logger.error('Metrics endpoint error', {
      error: error instanceof Error ? error.message : String(error)
    });

    res.status(500).json({
      error: 'Failed to retrieve metrics'
    });
  }
});

/**
 * Readiness probe for Kubernetes/Docker
 * GET /ready
 */
router.get('/ready', async (req: Request, res: Response) => {
  try {
    const dbHealth = cockroachDBConnectionManager.getHealthStatus();
    const isDbReady = dbHealth?.isHealthy ?? false;
    
    if (isDbReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        reason: 'Database not healthy'
      });
    }

  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: 'Readiness check failed'
    });
  }
});

export default router;
