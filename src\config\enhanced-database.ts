/**
 * Enhanced Database Configuration for CockroachDB
 * Integrates connection management, performance monitoring, and error handling
 */

import { PoolClient } from 'pg';
import { cockroachDBConnectionManager } from './supabase-connection-manager';
import { performanceMonitor } from '../utils/performance-monitor';
import logger from '../utils/logger';

interface QueryOptions {
  timeout?: number;
  retries?: number;
  logQuery?: boolean;
}

interface TransactionOptions {
  timeout?: number;
  isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
}

class EnhancedDatabase {
  /**
   * Execute a query with performance monitoring and error handling
   */
  public async query(text: string, params?: any[], options: QueryOptions = {}): Promise<any> {
    const startTime = Date.now();
    const { timeout = 30000, retries = 1, logQuery = false } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        if (logQuery) {
          logger.debug('Executing query', {
            query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
            params: params ? params.length : 0,
            attempt
          });
        }

        // Set query timeout
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Query timeout')), timeout);
        });

        const queryPromise = cockroachDBConnectionManager.query(text, params);
        const result = await Promise.race([queryPromise, timeoutPromise]);

        const duration = Date.now() - startTime;
        
        // Record successful query
        performanceMonitor.recordQuery(text, duration, true);
        
        if (duration > 1000) {
          logger.warn('Slow query completed', {
            duration,
            query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
            attempt
          });
        }

        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        const duration = Date.now() - startTime;
        
        // Record failed query
        performanceMonitor.recordQuery(text, duration, false, lastError.message);
        
        // Check if it's a connection error
        if (this.isConnectionError(lastError)) {
          performanceMonitor.recordConnectionIssue(lastError.message);
        }

        logger.error('Query execution failed', {
          error: lastError.message,
          query: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
          attempt,
          duration,
          willRetry: attempt < retries
        });

        // Don't retry for certain types of errors
        if (!this.shouldRetry(lastError) || attempt === retries) {
          break;
        }

        // Wait before retry with exponential backoff
        const retryDelay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    throw lastError;
  }

  /**
   * Get a database client for manual connection management
   */
  public async getClient(): Promise<PoolClient> {
    try {
      return await cockroachDBConnectionManager.getClient();
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      performanceMonitor.recordConnectionIssue(errorMsg);
      throw error;
    }
  }

  /**
   * Execute multiple queries in a transaction
   */
  public async transaction<T>(
    callback: (client: PoolClient) => Promise<T>,
    options: TransactionOptions = {}
  ): Promise<T> {
    const { timeout = 60000, isolationLevel } = options;
    const startTime = Date.now();
    let client: PoolClient | null = null;

    try {
      client = await this.getClient();
      
      // Begin transaction
      await client.query('BEGIN');
      
      // Set isolation level if specified
      if (isolationLevel) {
        await client.query(`SET TRANSACTION ISOLATION LEVEL ${isolationLevel}`);
      }

      // Set transaction timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Transaction timeout')), timeout);
      });

      const transactionPromise = callback(client);
      const result = await Promise.race([transactionPromise, timeoutPromise]);

      // Commit transaction
      await client.query('COMMIT');
      
      const duration = Date.now() - startTime;
      logger.debug('Transaction completed successfully', { duration });
      
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Rollback transaction
      if (client) {
        try {
          await client.query('ROLLBACK');
          logger.debug('Transaction rolled back', { duration });
        } catch (rollbackError) {
          logger.error('Failed to rollback transaction', {
            originalError: error instanceof Error ? error.message : String(error),
            rollbackError: rollbackError instanceof Error ? rollbackError.message : String(rollbackError)
          });
        }
      }

      logger.error('Transaction failed', {
        error: error instanceof Error ? error.message : String(error),
        duration
      });

      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Execute a query with automatic retry for connection issues
   */
  public async queryWithRetry(text: string, params?: any[], maxRetries: number = 3): Promise<any> {
    return this.query(text, params, { retries: maxRetries });
  }

  /**
   * Check if the database is healthy
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const result = await this.query('SELECT 1 as health_check', [], { timeout: 5000 });
      return result.rows.length > 0 && result.rows[0].health_check === 1;
    } catch (error) {
      logger.error('Database health check failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Get database performance metrics
   */
  public getPerformanceMetrics() {
    return {
      connection: cockroachDBConnectionManager.getMetrics(),
      queries: performanceMonitor.getMetrics(),
      health: cockroachDBConnectionManager.getHealthStatus(),
      recommendations: performanceMonitor.getPerformanceRecommendations()
    };
  }

  /**
   * Execute a prepared statement (for frequently used queries)
   */
  public async executePrepared(name: string, text: string, params?: any[]): Promise<any> {
    let client: PoolClient | null = null;
    const startTime = Date.now();

    try {
      client = await this.getClient();
      
      // Prepare statement if not already prepared
      try {
        await client.query(`PREPARE ${name} AS ${text}`);
      } catch (error) {
        // Statement might already be prepared, ignore error
        if (!(error instanceof Error) || !error.message.includes('already exists')) {
          throw error;
        }
      }

      // Execute prepared statement
      const result = await client.query(`EXECUTE ${name}${params ? `(${params.map(() => '$1').join(',')})` : ''}`, params);
      
      const duration = Date.now() - startTime;
      performanceMonitor.recordQuery(`EXECUTE ${name}`, duration, true);
      
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message : String(error);
      
      performanceMonitor.recordQuery(`EXECUTE ${name}`, duration, false, errorMsg);
      
      logger.error('Prepared statement execution failed', {
        name,
        error: errorMsg,
        duration
      });

      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Batch execute multiple queries efficiently
   */
  public async batchExecute(queries: Array<{ text: string; params?: any[] }>): Promise<any[]> {
    const startTime = Date.now();
    let client: PoolClient | null = null;

    try {
      client = await this.getClient();
      const results: any[] = [];

      for (const query of queries) {
        const result = await client.query(query.text, query.params);
        results.push(result);
      }

      const duration = Date.now() - startTime;
      logger.debug('Batch execution completed', {
        queryCount: queries.length,
        duration
      });

      return results;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Batch execution failed', {
        error: error instanceof Error ? error.message : String(error),
        queryCount: queries.length,
        duration
      });

      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  private isConnectionError(error: Error): boolean {
    const connectionErrorCodes = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNRESET',
      'EPIPE'
    ];

    return connectionErrorCodes.some(code => 
      error.message.includes(code) || (error as any).code === code
    );
  }

  private shouldRetry(error: Error): boolean {
    // Retry on connection errors and temporary failures
    const retryableErrors = [
      'connection terminated',
      'connection reset',
      'timeout',
      'ECONNRESET',
      'ETIMEDOUT',
      'ENOTFOUND'
    ];

    return retryableErrors.some(errorType => 
      error.message.toLowerCase().includes(errorType.toLowerCase())
    );
  }

  /**
   * Close all connections
   */
  public async close(): Promise<void> {
    await cockroachDBConnectionManager.close();
  }
}

// Export singleton instance
export const enhancedDatabase = new EnhancedDatabase();
export default enhancedDatabase;
