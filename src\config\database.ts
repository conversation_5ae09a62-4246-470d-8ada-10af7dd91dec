import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';
import logger from '../utils/logger';

// Load environment variables
dotenv.config();

// Track active clients
const activeClients = new Set<PoolClient>();

// Create connection options object optimized for CockroachDB
const connectionOptions: any = {
  host: process.env.PGHOST,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  database: process.env.PGDATABASE,
  port: parseInt(process.env.PGPORT || '26257'),
  // CockroachDB requires SSL with verify-full
  ssl: process.env.PGSSLMODE === 'verify-full' ? {
    rejectUnauthorized: true
  } : process.env.PGSSLMODE === 'require' ? {
    rejectUnauthorized: false
  } : false,
  // Optimized for CockroachDB cloud database
  max: parseInt(process.env.PG_MAX_CONNECTIONS || '15'), // Reduced for cloud database
  min: parseInt(process.env.PG_MIN_CONNECTIONS || '3'),
  idleTimeoutMillis: parseInt(process.env.PG_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: 15000, // Increased for CockroachDB cloud latency
  acquireTimeoutMillis: 8000,
  keepAlive: true,
  statement_timeout: 45000, // Increased for CockroachDB distributed queries
  query_timeout: 45000,
  application_name: `tempmail-api-${process.env.INSTANCE_ID || 'unknown'}`,
};

// Log sanitized connection configuration (without sensitive information)
logger.debug(`Database connection initialized: CockroachDB connection, SSL: ${connectionOptions.ssl ? 'enabled' : 'disabled'}`);

// Database connection configuration
const pool = new Pool(connectionOptions);

// Add connection event logging
pool.on('connect', (client: PoolClient) => {
  activeClients.add(client);
  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Client connected. Active connections: ${activeClients.size}`);
  }
});

pool.on('remove', (client: PoolClient) => {
  activeClients.delete(client);
  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Client removed. Active connections: ${activeClients.size}`);
  }
});

// Monitor connection pool less frequently
setInterval(() => {
  const idleCount = pool.idleCount;
  const totalCount = pool.totalCount;
  const waitingCount = pool.waitingCount;

  // Only log if pool is near capacity
  if (totalCount > pool.options.max * 0.8) {
    logger.warn(`DB Pool near capacity: ${totalCount}/${pool.options.max}`);
  }
}, 300000); // Check every 5 minutes instead of every minute

// Enhanced error handling
pool.on('error', (err: Error, client: PoolClient) => {
  logger.error('Unexpected database pool error:', err);

  if (client) {
    activeClients.delete(client);
    try {
      client.release(true); // Force destroy the client
    } catch (releaseError) {
      logger.error('Error releasing errored client:', releaseError as Error);
    }
  }
});

// Helper function to validate a client connection
async function validateClient(client: PoolClient): Promise<boolean> {
  try {
    await client.query('SELECT 1');
    return true;
  } catch (error) {
    logger.error('Client validation failed:', error as Error);
    return false;
  }
}

// Helper function to safely release a client
function safelyReleaseClient(client: PoolClient): void {
  if (!client || (client as any)._released) return;

  try {
    // Clear any auto-release timeout
    if ((client as any)._autoReleaseTimeout) {
      clearTimeout((client as any)._autoReleaseTimeout);
      (client as any)._autoReleaseTimeout = null;
    }

    (client as any)._released = true;
    // Release is actually synchronous despite the type definition
    client.release();
    activeClients.delete(client);
  } catch (error) {
    logger.error('Error safely releasing client:', error as Error);
    activeClients.delete(client);
  }
}

// Simplified getClient function
async function getClientImpl(): Promise<PoolClient> {
  try {
    return await pool.connect();
  } catch (error) {
    logger.error('Failed to get database connection:', error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
}

// Use the implementation as the exported function
const getClient = getClientImpl;

export { pool as default, getClient };



