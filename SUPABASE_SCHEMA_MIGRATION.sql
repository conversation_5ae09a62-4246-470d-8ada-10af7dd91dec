-- =====================================================
-- TempFly.io Complete Schema Migration for Supabase
-- =====================================================
-- This script creates the complete database schema for TempFly.io on Supabase
-- Execute this script in the Supabase SQL Editor

-- Step 1: Enable Required Extensions
-- ===================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- Step 2: Create Tables
-- =====================

-- Domains Table
CREATE TABLE IF NOT EXISTS domains (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_custom BOOLEAN DEFAULT false,
    mx_record VARCHAR(255),
    spf_record TEXT,
    dkim_record TEXT,
    dmarc_record TEXT,
    verification_status VARCHAR(50) DEFAULT 'pending',
    last_verified_at TIMESTAMP WITH TIME ZONE,
    smtp_enabled BOOLEAN DEFAULT false,
    smtp_server VARCHAR(255),
    smtp_port INTEGER DEFAULT 25,
    smtp_username VARCHAR(255),
    smtp_password VARCHAR(255),
    smtp_secure BOOLEAN DEFAULT false
);

-- Inboxes Table
CREATE TABLE IF NOT EXISTS inboxes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    domain VARCHAR(255) NOT NULL,
    address VARCHAR(320) NOT NULL UNIQUE,
    email VARCHAR(320),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    api_key_id UUID,
    rapidapi_key VARCHAR(255),
    forwarding_enabled BOOLEAN DEFAULT false,
    forwarding_email VARCHAR(320),
    forwarding_verified BOOLEAN DEFAULT false,
    verification_token UUID DEFAULT uuid_generate_v4(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Emails Table
CREATE TABLE IF NOT EXISTS emails (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inbox_id UUID NOT NULL REFERENCES inboxes(id) ON DELETE CASCADE,
    from_address VARCHAR(320) NOT NULL,
    from_name VARCHAR(255),
    subject TEXT,
    text_body TEXT,
    text_content TEXT,
    html_body TEXT,
    html_content TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    received_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP WITH TIME ZONE,
    headers JSONB,
    to_address VARCHAR(320),
    cc_address TEXT,
    bcc_address TEXT,
    reply_to VARCHAR(320),
    message_id VARCHAR(255),
    in_reply_to VARCHAR(255),
    references_field TEXT,
    priority VARCHAR(50),
    spam_score FLOAT,
    is_spam BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    source TEXT,
    raw_size INTEGER,
    has_attachments BOOLEAN DEFAULT false
);

-- Attachments Table
CREATE TABLE IF NOT EXISTS attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email_id UUID NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    content_type VARCHAR(255),
    content_id VARCHAR(255),
    size INTEGER,
    content BYTEA,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    storage_path TEXT,
    is_inline BOOLEAN DEFAULT false
);

-- API Keys Table
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    rate_limit INTEGER,
    permissions JSONB
);

-- Forwarding Verifications Table
CREATE TABLE IF NOT EXISTS forwarding_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inbox_id UUID NOT NULL REFERENCES inboxes(id) ON DELETE CASCADE,
    email VARCHAR(320) NOT NULL,
    token UUID NOT NULL DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),
    is_verified BOOLEAN DEFAULT false
);

-- Usage Logs Table
CREATE TABLE IF NOT EXISTS usage_logs (
    id SERIAL PRIMARY KEY,
    api_key_id UUID REFERENCES api_keys(id) ON DELETE SET NULL,
    rapidapi_key VARCHAR(255),
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time INTEGER,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    request_id VARCHAR(255),
    request_body JSONB,
    response_body JSONB
);

-- RapidAPI Usage Table
CREATE TABLE IF NOT EXISTS rapidapi_usage (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    request_id VARCHAR(255),
    rate_limit_limit INTEGER NOT NULL DEFAULT 0,
    rate_limit_remaining INTEGER NOT NULL DEFAULT 0,
    rate_limit_reset INTEGER NOT NULL DEFAULT 0,
    plan_type VARCHAR(50) NOT NULL DEFAULT 'UNKNOWN',
    region VARCHAR(50),
    version VARCHAR(50),
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Maintenance Logs Table
CREATE TABLE IF NOT EXISTS maintenance_logs (
    id SERIAL PRIMARY KEY,
    operation VARCHAR(255) NOT NULL,
    details TEXT,
    affected_rows INTEGER,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    execution_time INTEGER,
    status VARCHAR(50) DEFAULT 'success',
    error_message TEXT
);

-- Step 3: Create Indexes for Performance
-- ======================================

-- Inboxes indexes
CREATE INDEX IF NOT EXISTS idx_inboxes_address ON inboxes(address);
CREATE INDEX IF NOT EXISTS idx_inboxes_domain ON inboxes(domain);
CREATE INDEX IF NOT EXISTS idx_inboxes_rapidapi_key ON inboxes(rapidapi_key);
CREATE INDEX IF NOT EXISTS idx_inboxes_api_key_id ON inboxes(api_key_id);
CREATE INDEX IF NOT EXISTS idx_inboxes_created_at ON inboxes(created_at);
CREATE INDEX IF NOT EXISTS idx_inboxes_expiry_date ON inboxes(expiry_date);
CREATE INDEX IF NOT EXISTS idx_inboxes_is_active ON inboxes(is_active);
CREATE INDEX IF NOT EXISTS idx_inboxes_name ON inboxes(name);

-- Composite indexes for performance
CREATE INDEX IF NOT EXISTS idx_inboxes_composite ON inboxes(rapidapi_key, is_active, expiry_date);

-- Emails indexes
CREATE INDEX IF NOT EXISTS idx_emails_inbox_id ON emails(inbox_id);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);
CREATE INDEX IF NOT EXISTS idx_emails_from_address ON emails(from_address);
CREATE INDEX IF NOT EXISTS idx_emails_read_at ON emails(read_at);
CREATE INDEX IF NOT EXISTS idx_emails_is_spam ON emails(is_spam);

-- Composite indexes for email filtering
CREATE INDEX IF NOT EXISTS idx_emails_inbox_filters ON emails(inbox_id, from_address, received_at);

-- Full-text search index
CREATE INDEX IF NOT EXISTS idx_emails_from_address_gin ON emails USING gin(to_tsvector('english', from_address));

-- Attachments indexes
CREATE INDEX IF NOT EXISTS idx_attachments_email_id ON attachments(email_id);
CREATE INDEX IF NOT EXISTS idx_attachments_content_id ON attachments(content_id);

-- API Keys indexes
CREATE INDEX IF NOT EXISTS idx_api_keys_key ON api_keys(key);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);

-- Forwarding verifications indexes
CREATE INDEX IF NOT EXISTS idx_forwarding_verifications_inbox_id ON forwarding_verifications(inbox_id);
CREATE INDEX IF NOT EXISTS idx_forwarding_verifications_token ON forwarding_verifications(token);

-- Usage logs indexes
CREATE INDEX IF NOT EXISTS idx_usage_logs_api_key_id ON usage_logs(api_key_id);
CREATE INDEX IF NOT EXISTS idx_usage_logs_rapidapi_key ON usage_logs(rapidapi_key);
CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at ON usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_logs_endpoint ON usage_logs(endpoint);

-- RapidAPI usage indexes
CREATE INDEX IF NOT EXISTS idx_rapidapi_usage_user_id ON rapidapi_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_rapidapi_usage_created_at ON rapidapi_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_rapidapi_usage_endpoint ON rapidapi_usage(endpoint);
CREATE INDEX IF NOT EXISTS idx_rapidapi_usage_plan_type ON rapidapi_usage(plan_type);

-- Maintenance logs indexes
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_operation ON maintenance_logs(operation);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_executed_at ON maintenance_logs(executed_at);

-- Step 4: Insert Default Domains
-- ===============================
INSERT INTO domains (domain, is_active, is_custom, smtp_enabled)
VALUES
    ('boxqix.com', true, false, true),
    ('fitnessfocusgym.site', true, false, true),
    ('gourmetguidecafe.site', true, false, true),
    ('greenthumbgardens.store', true, false, true),
    ('joibcddsd.online', true, false, true),
    ('mailvu.space', true, false, true),
    ('probox.site', true, false, true),
    ('rubymail.site', true, false, true),
    ('trendygadgetshop.store', true, false, true),
    ('umorjjsassaas.store', true, false, true),
    ('umorjjsdsd.site', true, false, true),
    ('wellnesswavecenter.site', true, false, true)
ON CONFLICT (domain) DO NOTHING;

-- Step 5: Create Maintenance Functions
-- ====================================

-- Function to clean up expired inboxes
CREATE OR REPLACE FUNCTION cleanup_expired_inboxes()
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
    start_time TIMESTAMP WITH TIME ZONE;
    end_time TIMESTAMP WITH TIME ZONE;
    execution_time INTEGER;
BEGIN
    start_time := clock_timestamp();

    -- Mark expired inboxes as inactive
    WITH updated_inboxes AS (
        UPDATE inboxes
        SET is_active = false
        WHERE expiry_date IS NOT NULL
        AND expiry_date < NOW()
        AND is_active = true
        RETURNING id
    )
    SELECT COUNT(*) INTO affected_rows FROM updated_inboxes;

    end_time := clock_timestamp();
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;

    -- Log the operation
    IF affected_rows > 0 THEN
        INSERT INTO maintenance_logs (
            operation,
            details,
            affected_rows,
            execution_time,
            status
        )
        VALUES (
            'cleanup_expired_inboxes_cron',
            'Scheduled cleanup of expired inboxes via pg_cron',
            affected_rows,
            execution_time,
            'success'
        );
    END IF;

    RETURN affected_rows;
EXCEPTION WHEN OTHERS THEN
    INSERT INTO maintenance_logs (
        operation,
        details,
        affected_rows,
        execution_time,
        status,
        error_message
    )
    VALUES (
        'cleanup_expired_inboxes_cron',
        'Scheduled cleanup of expired inboxes via pg_cron failed',
        0,
        NULL,
        'error',
        SQLERRM
    );
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to purge old maintenance logs
CREATE OR REPLACE FUNCTION purge_old_maintenance_logs()
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    DELETE FROM maintenance_logs
    WHERE executed_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    IF affected_rows > 0 THEN
        INSERT INTO maintenance_logs (
            operation,
            details,
            affected_rows
        )
        VALUES (
            'purge_old_maintenance_logs',
            'Purged maintenance logs older than 30 days',
            affected_rows
        );
    END IF;

    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Setup pg_cron Jobs
-- ===========================

-- Schedule inbox cleanup every 15 minutes
SELECT cron.schedule(
    'cleanup-expired-inboxes',
    '*/15 * * * *',
    $$SELECT cleanup_expired_inboxes()$$
);

-- Schedule log purging daily at 3:30 AM
SELECT cron.schedule(
    'purge-old-maintenance-logs',
    '30 3 * * *',
    $$SELECT purge_old_maintenance_logs()$$
);

-- Step 7: Create Username Sync Trigger
-- =====================================

-- Function to sync username with name
CREATE OR REPLACE FUNCTION sync_username_with_name()
RETURNS TRIGGER AS $$
BEGIN
    NEW.username := NEW.name;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER sync_username_with_name_trigger
BEFORE INSERT OR UPDATE ON inboxes
FOR EACH ROW
EXECUTE FUNCTION sync_username_with_name();

-- Step 8: Verification Queries
-- =============================

-- Verify scheduled jobs
SELECT * FROM cron.job;

-- Verify tables were created
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;

-- Verify extensions are enabled
SELECT extname FROM pg_extension
WHERE extname IN ('uuid-ossp', 'pgcrypto', 'pg_cron');

-- =====================================================
-- Migration Complete!
-- =====================================================
-- Your TempFly.io database is now ready on Supabase
-- with all tables, indexes, functions, and scheduled jobs.
